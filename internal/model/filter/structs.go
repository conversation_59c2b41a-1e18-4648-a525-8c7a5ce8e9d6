/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-09-02
 */
package filter

import (
	"database/sql/driver"
	"encoding/json"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gorm.io/gorm"
)

type TenantIDArray []string

type FilterRuleConfig struct {
	Rules    []FilterRule `json:"rules"`
	RawRules string       `json:"rawRules"` // 原始手写规则，有原始手写规则就不考虑rules
}

type DeduplicationRuleConfig struct {
	Keys       []string `json:"keys"`
	TimeWindow int64    `json:"timeWindow"` // 窗口长度，单位秒
}

type FilterRule struct {
	Field string      `json:"field"`
	Op    string      `json:"op"`
	Value interface{} `json:"value"`
}

type RuleTable struct {
	gorm.Model
	RuleID       string                     `json:"ruleID" gorm:"column:rule_id"`
	BusinessID   string                     `json:"businessID" gorm:"column:business_id"`
	TenantID     *TenantIDArray             `json:"tenantID" gorm:"column:tenant_id"`
	Name         string                     `json:"name" gorm:"column:name"`
	Desc         string                     `json:"desc" gorm:"column:desc"`
	Type         socCommon.FilterRuleType   `json:"type" gorm:"column:type"`
	FilterConfig *FilterRuleConfig          `json:"filter_config" gorm:"column:filter_config"`
	DedupConfig  *DeduplicationRuleConfig   `json:"dedupConfig" gorm:"column:dedup_config"`
	Status       socCommon.FilterRuleStatus `json:"status" gorm:"column:status"`
	Version      int64                      `json:"version" gorm:"column:version"`
}

// Query filter structs
type FilterRuleQueryFilter struct {
	Page, PerPage int
	RuleID        string                     `json:"ruleID"`
	BusinessID    string                     `json:"businessID"`
	TenantIDs     []string                   `json:"tenantIDs"`
	Name          string                     `json:"name"`
	Type          socCommon.FilterRuleType   `json:"type"`
	Status        socCommon.FilterRuleStatus `json:"status"`
	Version       int64                      `json:"version"`
	Order         string                     `json:"order"`
}

func (t *RuleTable) TableName() string {
	return socCommon.FilterRuleTableName
}

// 实现 GORM 序列化接口
func (*TenantIDArray) GormDataType() string {
	return "json"
}

func (o *TenantIDArray) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return socCommon.ErrGormScanJsonAssertToBytes
	}
	return json.Unmarshal(bytes, o)
}

func (o *TenantIDArray) Value() (driver.Value, error) {
	return json.Marshal(o)
}

// 实现 GORM 序列化接口
func (*FilterRuleConfig) GormDataType() string {
	return "json"
}

func (o *FilterRuleConfig) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return socCommon.ErrGormScanJsonAssertToBytes
	}
	return json.Unmarshal(bytes, o)
}

func (o *FilterRuleConfig) Value() (driver.Value, error) {
	return json.Marshal(o)
}

// 实现 GORM 序列化接口
func (*DeduplicationRuleConfig) GormDataType() string {
	return "json"
}

func (o *DeduplicationRuleConfig) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return socCommon.ErrGormScanJsonAssertToBytes
	}
	return json.Unmarshal(bytes, o)
}

func (o *DeduplicationRuleConfig) Value() (driver.Value, error) {
	return json.Marshal(o)
}
