/**
 * @note
 * biz
 *
 * <AUTHOR>
 * @date 	2025-09-03
 */
package biz

import (
	"context"
	"gorm.io/gorm"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
)

// BizTable implementation methods
func (m *BizModelImpl) CreateBiz(ctx context.Context, biz *BizTable) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	biz.ID = uint(idgen.GetID())
	return int64(biz.ID), db.Create(biz).Error
}

func (m *BizModelImpl) DeleteBiz(ctx context.Context, businessID string) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	return db.Where("business_id = ?", businessID).Delete(&BizTable{}).Error
}

func (m *BizModelImpl) UpdateBizByBizID(ctx context.Context, businessID string, name *string, desc *string, originBusinessID *int64) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&BizTable{})
	updateMap := make(map[string]interface{})

	if name != nil {
		updateMap["name"] = *name
	}
	if desc != nil {
		updateMap["desc"] = *desc
	}
	if originBusinessID != nil {
		updateMap["origin_business_id"] = *originBusinessID
	}

	if len(updateMap) == 0 {
		return nil // 没有需要更新的字段
	}

	return db.Where("business_id = ?", businessID).Updates(updateMap).Error
}

func (m *BizModelImpl) QueryBizBySeveralConditions(ctx context.Context, filter BizQueryFilter) ([]*BizTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&BizTable{})

	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleBizQueryConditions(ctx, filter, db)

	tbs := make([]*BizTable, 0)
	if filter.Order != common.StringEmpty {
		db = db.Order(filter.Order)
	}
	db = db.Find(&tbs)
	return tbs, db.Error
}

func (m *BizModelImpl) QueryBizCountBySeveralConditions(ctx context.Context, filter BizQueryFilter) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&BizTable{})

	db = assembleBizQueryConditions(ctx, filter, db)
	var count int64
	db = db.Count(&count)
	return count, db.Error
}

func assembleBizQueryConditions(ctx context.Context, filter BizQueryFilter, db *gorm.DB) *gorm.DB {
	if filter.BusinessID != common.StringEmpty {
		db = db.Where("business_id = ?", filter.BusinessID)
	}
	if filter.Name != common.StringEmpty {
		db = db.Where("name LIKE ?", "%"+filter.Name+"%")
	}
	if filter.OriginBusinessID != 0 {
		db = db.Where("origin_business_id = ?", filter.OriginBusinessID)
	}
	return db
}
