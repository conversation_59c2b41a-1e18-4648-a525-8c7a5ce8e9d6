/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-09-01
 */
package biz

import (
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gorm.io/gorm"
)

type BizTable struct {
	gorm.Model
	BusinessID       string `json:"businessID" gorm:"column:business_id"` // 唯一键
	Name             string `json:"name" gorm:"column:name"`
	Desc             string `json:"desc" gorm:"column:desc"`
	OriginBusinessID int64  `json:"originBusinessID" gorm:"column:origin_business_id"` // 对应老SOC的businessID
}

type AlarmTypeTable struct {
	gorm.Model
	TypeID       string `json:"typeID" gorm:"column:type_id"`         // 唯一键
	BusinessID   string `json:"businessID" gorm:"column:business_id"` // 所属业务ID
	Name         string `json:"name" gorm:"column:name"`
	Desc         string `json:"desc" gorm:"column:desc"`
	OriginTypeID int64  `json:"originTypeID" gorm:"column:origin_type_id"`
}

type TenantTable struct {
	gorm.Model
	TenantID   string `json:"tenantID" gorm:"column:tenant_id"` // 唯一键
	Name       string `json:"name" gorm:"column:name"`
	Desc       string `json:"desc" gorm:"column:desc"`
	ExchangeID int64  `json:"exchangeID" gorm:"column:exchange_id"`
}

func (t *BizTable) TableName() string {
	return socCommon.BizTableName
}

func (t *AlarmTypeTable) TableName() string {
	return socCommon.AlarmTypeTableName
}

func (t *TenantTable) TableName() string {
	return socCommon.TenantTableName
}

// Query filter structs
type BizQueryFilter struct {
	Page, PerPage    int
	BusinessID       string `json:"businessID"`
	Name             string `json:"name"`
	OriginBusinessID int64  `json:"originBusinessID"`
	Order            string `json:"order"`
}

type AlarmTypeQueryFilter struct {
	Page, PerPage int
	TypeID        string `json:"typeID"`
	BusinessID    string `json:"businessID"`
	Name          string `json:"name"`
	OriginTypeID  int64  `json:"originTypeID"`
	Order         string `json:"order"`
}

type TenantQueryFilter struct {
	Page, PerPage int
	TenantIDs     []string `json:"tenantIDs"`
	Name          string   `json:"name"`
	ExchangeID    int64    `json:"exchangeID"`
	Order         string   `json:"order"`
}
