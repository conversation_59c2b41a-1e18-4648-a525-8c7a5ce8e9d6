/**
 * @note
 * tenant
 *
 * <AUTHOR>
 * @date 	2025-09-03
 */
package biz

import (
	"context"
	"gorm.io/gorm"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
)

// TenantTable implementation methods
func (m *BizModelImpl) CreateTenant(ctx context.Context, tenant *TenantTable) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	tenant.ID = uint(idgen.GetID())
	return int64(tenant.ID), db.Create(tenant).Error
}

func (m *BizModelImpl) DeleteTenants(ctx context.Context, tenantIDs []string) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	return db.Where("tenant_id in (?)", tenantIDs).Delete(&TenantTable{}).Error
}

func (m *BizModelImpl) UpdateTenantByTenantID(ctx context.Context, tenantID string, name *string, desc *string, exchangeID *int64) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&TenantTable{})
	updateMap := make(map[string]interface{})

	if name != nil {
		updateMap["name"] = *name
	}
	if desc != nil {
		updateMap["desc"] = *desc
	}
	if exchangeID != nil {
		updateMap["exchange_id"] = *exchangeID
	}

	if len(updateMap) == 0 {
		return nil // 没有需要更新的字段
	}

	return db.Where("tenant_id = ?", tenantID).Updates(updateMap).Error
}

func (m *BizModelImpl) QueryTenantBySeveralConditions(ctx context.Context, filter TenantQueryFilter) ([]*TenantTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&TenantTable{})

	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleTenantQueryConditions(ctx, filter, db)

	tbs := make([]*TenantTable, 0)
	if filter.Order != common.StringEmpty {
		db = db.Order(filter.Order)
	}
	db = db.Find(&tbs)
	return tbs, db.Error
}

func (m *BizModelImpl) QueryTenantCountBySeveralConditions(ctx context.Context, filter TenantQueryFilter) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&TenantTable{})

	db = assembleTenantQueryConditions(ctx, filter, db)
	var count int64
	db = db.Count(&count)
	return count, db.Error
}

func assembleTenantQueryConditions(ctx context.Context, filter TenantQueryFilter, db *gorm.DB) *gorm.DB {
	if len(filter.TenantIDs) > 0 {
		db = db.Where("tenant_id in (?)", filter.TenantIDs)
	}
	if filter.Name != common.StringEmpty {
		db = db.Where("name LIKE ?", "%"+filter.Name+"%")
	}
	if filter.ExchangeID != 0 {
		db = db.Where("exchange_id = ?", filter.ExchangeID)
	}
	return db
}
