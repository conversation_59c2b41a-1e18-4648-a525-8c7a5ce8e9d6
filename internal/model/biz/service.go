/**
 * @note
 * biz.go
 *
 * <AUTHOR>
 * @date 	2025-09-01
 */
package biz

import (
	"context"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	"gitlab.docsl.com/security/common/mysql"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gorm.io/gorm"
)

type BizModel interface {
	// BizTable methods
	CreateBiz(ctx context.Context, biz *BizTable) (int64, error)
	DeleteBiz(ctx context.Context, businessID string) error
	UpdateBizByBizID(ctx context.Context, businessID string, name *string, desc *string, originBusinessID *int64) error
	QueryBizBySeveralConditions(ctx context.Context, filter BizQueryFilter) ([]*BizTable, error)
	QueryBizCountBySeveralConditions(ctx context.Context, filter BizQueryFilter) (int64, error)

	// AlarmTypeTable methods
	CreateAlarmType(ctx context.Context, alarmType *AlarmTypeTable) (int64, error)
	DeleteAlarmType(ctx context.Context, typeID string) error
	UpdateAlarmTypeByTypeID(ctx context.Context, typeID string, businessID *string, name *string, desc *string, originTypeID *int64) error
	QueryAlarmTypeBySeveralConditions(ctx context.Context, filter AlarmTypeQueryFilter) ([]*AlarmTypeTable, error)
	QueryAlarmTypeCountBySeveralConditions(ctx context.Context, filter AlarmTypeQueryFilter) (int64, error)

	// TenantTable methods
	CreateTenant(ctx context.Context, tenant *TenantTable) (int64, error)
	DeleteTenants(ctx context.Context, tenantIDs []string) error
	UpdateTenantByTenantID(ctx context.Context, tenantID string, name *string, desc *string, exchangeID *int64) error
	QueryTenantBySeveralConditions(ctx context.Context, filter TenantQueryFilter) ([]*TenantTable, error)
	QueryTenantCountBySeveralConditions(ctx context.Context, filter TenantQueryFilter) (int64, error)
}

type BizModelImpl struct{}

func (m *BizModelImpl) getDB(ctx context.Context) (db *gorm.DB, err error) {
	return mysql.GetDB(socCommon.DBName, false, common.GetLogger(ctx))
}

var DefaultService BizModel = &BizModelImpl{}

// Public functions for BizTable
func CreateBiz(ctx context.Context, biz *BizTable) (int64, error) {
	return DefaultService.CreateBiz(ctx, biz)
}

func DeleteBiz(ctx context.Context, businessID string) error {
	return DefaultService.DeleteBiz(ctx, businessID)
}

func UpdateBizByBizID(ctx context.Context, businessID string, name *string, desc *string, originBusinessID *int64) error {
	return DefaultService.UpdateBizByBizID(ctx, businessID, name, desc, originBusinessID)
}

func QueryBizBySeveralConditions(ctx context.Context, filter BizQueryFilter) ([]*BizTable, error) {
	return DefaultService.QueryBizBySeveralConditions(ctx, filter)
}

func QueryBizCountBySeveralConditions(ctx context.Context, filter BizQueryFilter) (int64, error) {
	return DefaultService.QueryBizCountBySeveralConditions(ctx, filter)
}

// BizTable implementation methods
func (m *BizModelImpl) CreateBiz(ctx context.Context, biz *BizTable) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	biz.ID = uint(idgen.GetID())
	return int64(biz.ID), db.Create(biz).Error
}

func (m *BizModelImpl) DeleteBiz(ctx context.Context, businessID string) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	return db.Where("business_id = ?", businessID).Delete(&BizTable{}).Error
}

func (m *BizModelImpl) UpdateBizByBizID(ctx context.Context, businessID string, name *string, desc *string, originBusinessID *int64) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&BizTable{})
	updateMap := make(map[string]interface{})

	if name != nil {
		updateMap["name"] = *name
	}
	if desc != nil {
		updateMap["desc"] = *desc
	}
	if originBusinessID != nil {
		updateMap["origin_business_id"] = *originBusinessID
	}

	if len(updateMap) == 0 {
		return nil // 没有需要更新的字段
	}

	return db.Where("business_id = ?", businessID).Updates(updateMap).Error
}

func (m *BizModelImpl) QueryBizBySeveralConditions(ctx context.Context, filter BizQueryFilter) ([]*BizTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&BizTable{})

	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleBizQueryConditions(ctx, filter, db)

	tbs := make([]*BizTable, 0)
	if filter.Order != common.StringEmpty {
		db = db.Order(filter.Order)
	}
	db = db.Find(&tbs)
	return tbs, db.Error
}

func (m *BizModelImpl) QueryBizCountBySeveralConditions(ctx context.Context, filter BizQueryFilter) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&BizTable{})

	db = assembleBizQueryConditions(ctx, filter, db)
	var count int64
	db = db.Count(&count)
	return count, db.Error
}

func assembleBizQueryConditions(ctx context.Context, filter BizQueryFilter, db *gorm.DB) *gorm.DB {
	if filter.BusinessID != common.StringEmpty {
		db = db.Where("business_id = ?", filter.BusinessID)
	}
	if filter.Name != common.StringEmpty {
		db = db.Where("name LIKE ?", "%"+filter.Name+"%")
	}
	if filter.OriginBusinessID != 0 {
		db = db.Where("origin_business_id = ?", filter.OriginBusinessID)
	}
	return db
}

// Public functions for AlarmTypeTable
func CreateAlarmType(ctx context.Context, alarmType *AlarmTypeTable) (int64, error) {
	return DefaultService.CreateAlarmType(ctx, alarmType)
}

func DeleteAlarmType(ctx context.Context, typeID string) error {
	return DefaultService.DeleteAlarmType(ctx, typeID)
}

func UpdateAlarmTypeByTypeID(ctx context.Context, typeID string, businessID *string, name *string, desc *string, originTypeID *int64) error {
	return DefaultService.UpdateAlarmTypeByTypeID(ctx, typeID, businessID, name, desc, originTypeID)
}

func QueryAlarmTypeBySeveralConditions(ctx context.Context, filter AlarmTypeQueryFilter) ([]*AlarmTypeTable, error) {
	return DefaultService.QueryAlarmTypeBySeveralConditions(ctx, filter)
}

func QueryAlarmTypeCountBySeveralConditions(ctx context.Context, filter AlarmTypeQueryFilter) (int64, error) {
	return DefaultService.QueryAlarmTypeCountBySeveralConditions(ctx, filter)
}

// AlarmTypeTable implementation methods
func (m *BizModelImpl) CreateAlarmType(ctx context.Context, alarmType *AlarmTypeTable) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	alarmType.ID = uint(idgen.GetID())
	return int64(alarmType.ID), db.Create(alarmType).Error
}

func (m *BizModelImpl) DeleteAlarmType(ctx context.Context, typeID string) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	return db.Where("type_id = ?", typeID).Delete(&AlarmTypeTable{}).Error
}

func (m *BizModelImpl) UpdateAlarmTypeByTypeID(ctx context.Context, typeID string, businessID *string, name *string, desc *string, originTypeID *int64) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&AlarmTypeTable{})
	updateMap := make(map[string]interface{})

	if businessID != nil {
		updateMap["business_id"] = *businessID
	}
	if name != nil {
		updateMap["name"] = *name
	}
	if desc != nil {
		updateMap["desc"] = *desc
	}
	if originTypeID != nil {
		updateMap["origin_type_id"] = *originTypeID
	}

	if len(updateMap) == 0 {
		return nil // 没有需要更新的字段
	}

	return db.Where("type_id = ?", typeID).Updates(updateMap).Error
}

func (m *BizModelImpl) QueryAlarmTypeBySeveralConditions(ctx context.Context, filter AlarmTypeQueryFilter) ([]*AlarmTypeTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AlarmTypeTable{})

	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleAlarmTypeQueryConditions(ctx, filter, db)

	tbs := make([]*AlarmTypeTable, 0)
	if filter.Order != common.StringEmpty {
		db = db.Order(filter.Order)
	}
	db = db.Find(&tbs)
	return tbs, db.Error
}

func (m *BizModelImpl) QueryAlarmTypeCountBySeveralConditions(ctx context.Context, filter AlarmTypeQueryFilter) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&AlarmTypeTable{})

	db = assembleAlarmTypeQueryConditions(ctx, filter, db)
	var count int64
	db = db.Count(&count)
	return count, db.Error
}

func assembleAlarmTypeQueryConditions(ctx context.Context, filter AlarmTypeQueryFilter, db *gorm.DB) *gorm.DB {
	if filter.TypeID != common.StringEmpty {
		db = db.Where("type_id = ?", filter.TypeID)
	}
	if filter.BusinessID != common.StringEmpty {
		db = db.Where("business_id = ?", filter.BusinessID)
	}
	if filter.Name != common.StringEmpty {
		db = db.Where("name LIKE ?", "%"+filter.Name+"%")
	}
	if filter.OriginTypeID != 0 {
		db = db.Where("origin_type_id = ?", filter.OriginTypeID)
	}
	return db
}

// Public functions for TenantTable
func CreateTenant(ctx context.Context, tenant *TenantTable) (int64, error) {
	return DefaultService.CreateTenant(ctx, tenant)
}

func DeleteTenants(ctx context.Context, tenantIDs []string) error {
	return DefaultService.DeleteTenants(ctx, tenantIDs)
}

func UpdateTenantByTenantID(ctx context.Context, tenantID string, name *string, desc *string, exchangeID *int64) error {
	return DefaultService.UpdateTenantByTenantID(ctx, tenantID, name, desc, exchangeID)
}

func QueryTenantBySeveralConditions(ctx context.Context, filter TenantQueryFilter) ([]*TenantTable, error) {
	return DefaultService.QueryTenantBySeveralConditions(ctx, filter)
}

func QueryTenantCountBySeveralConditions(ctx context.Context, filter TenantQueryFilter) (int64, error) {
	return DefaultService.QueryTenantCountBySeveralConditions(ctx, filter)
}

// TenantTable implementation methods
func (m *BizModelImpl) CreateTenant(ctx context.Context, tenant *TenantTable) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	tenant.ID = uint(idgen.GetID())
	return int64(tenant.ID), db.Create(tenant).Error
}

func (m *BizModelImpl) DeleteTenants(ctx context.Context, tenantIDs []string) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	return db.Where("tenant_id in (?)", tenantIDs).Delete(&TenantTable{}).Error
}

func (m *BizModelImpl) UpdateTenantByTenantID(ctx context.Context, tenantID string, name *string, desc *string, exchangeID *int64) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&TenantTable{})
	updateMap := make(map[string]interface{})

	if name != nil {
		updateMap["name"] = *name
	}
	if desc != nil {
		updateMap["desc"] = *desc
	}
	if exchangeID != nil {
		updateMap["exchange_id"] = *exchangeID
	}

	if len(updateMap) == 0 {
		return nil // 没有需要更新的字段
	}

	return db.Where("tenant_id = ?", tenantID).Updates(updateMap).Error
}

func (m *BizModelImpl) QueryTenantBySeveralConditions(ctx context.Context, filter TenantQueryFilter) ([]*TenantTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&TenantTable{})

	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleTenantQueryConditions(ctx, filter, db)

	tbs := make([]*TenantTable, 0)
	if filter.Order != common.StringEmpty {
		db = db.Order(filter.Order)
	}
	db = db.Find(&tbs)
	return tbs, db.Error
}

func (m *BizModelImpl) QueryTenantCountBySeveralConditions(ctx context.Context, filter TenantQueryFilter) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&TenantTable{})

	db = assembleTenantQueryConditions(ctx, filter, db)
	var count int64
	db = db.Count(&count)
	return count, db.Error
}

func assembleTenantQueryConditions(ctx context.Context, filter TenantQueryFilter, db *gorm.DB) *gorm.DB {
	if len(filter.TenantIDs) > 0 {
		db = db.Where("tenant_id in (?)", filter.TenantIDs)
	}
	if filter.Name != common.StringEmpty {
		db = db.Where("name LIKE ?", "%"+filter.Name+"%")
	}
	if filter.ExchangeID != 0 {
		db = db.Where("exchange_id = ?", filter.ExchangeID)
	}
	return db
}
