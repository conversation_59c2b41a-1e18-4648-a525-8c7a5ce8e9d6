/**
 * @note
 * biz.go
 *
 * <AUTHOR>
 * @date 	2025-09-01
 */
package biz

import (
	"context"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/mysql"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gorm.io/gorm"
)

type BizModel interface {
	// BizTable methods
	CreateBiz(ctx context.Context, biz *BizTable) (int64, error)
	DeleteBiz(ctx context.Context, businessID string) error
	UpdateBizByBizID(ctx context.Context, businessID string, name *string, desc *string, originBusinessID *int64) error
	QueryBizBySeveralConditions(ctx context.Context, filter BizQueryFilter) ([]*BizTable, error)
	QueryBizCountBySeveralConditions(ctx context.Context, filter BizQueryFilter) (int64, error)

	// AlarmTypeTable methods
	CreateAlarmType(ctx context.Context, alarmType *AlarmTypeTable) (int64, error)
	DeleteAlarmType(ctx context.Context, typeID string) error
	UpdateAlarmTypeByTypeID(ctx context.Context, typeID string, businessID *string, name *string, desc *string, originTypeID *int64) error
	QueryAlarmTypeBySeveralConditions(ctx context.Context, filter AlarmTypeQueryFilter) ([]*AlarmTypeTable, error)
	QueryAlarmTypeCountBySeveralConditions(ctx context.Context, filter AlarmTypeQueryFilter) (int64, error)

	// TenantTable methods
	CreateTenant(ctx context.Context, tenant *TenantTable) (int64, error)
	DeleteTenants(ctx context.Context, tenantIDs []string) error
	UpdateTenantByTenantID(ctx context.Context, tenantID string, name *string, desc *string, exchangeID *int64) error
	QueryTenantBySeveralConditions(ctx context.Context, filter TenantQueryFilter) ([]*TenantTable, error)
	QueryTenantCountBySeveralConditions(ctx context.Context, filter TenantQueryFilter) (int64, error)
}

type BizModelImpl struct{}

func (m *BizModelImpl) getDB(ctx context.Context) (db *gorm.DB, err error) {
	return mysql.GetDB(socCommon.DBName, false, common.GetLogger(ctx))
}

var DefaultService BizModel = &BizModelImpl{}

// Public functions for BizTable
func CreateBiz(ctx context.Context, biz *BizTable) (int64, error) {
	return DefaultService.CreateBiz(ctx, biz)
}

func DeleteBiz(ctx context.Context, businessID string) error {
	return DefaultService.DeleteBiz(ctx, businessID)
}

func UpdateBizByBizID(ctx context.Context, businessID string, name *string, desc *string, originBusinessID *int64) error {
	return DefaultService.UpdateBizByBizID(ctx, businessID, name, desc, originBusinessID)
}

func QueryBizBySeveralConditions(ctx context.Context, filter BizQueryFilter) ([]*BizTable, error) {
	return DefaultService.QueryBizBySeveralConditions(ctx, filter)
}

func QueryBizCountBySeveralConditions(ctx context.Context, filter BizQueryFilter) (int64, error) {
	return DefaultService.QueryBizCountBySeveralConditions(ctx, filter)
}

// Public functions for AlarmTypeTable
func CreateAlarmType(ctx context.Context, alarmType *AlarmTypeTable) (int64, error) {
	return DefaultService.CreateAlarmType(ctx, alarmType)
}

func DeleteAlarmType(ctx context.Context, typeID string) error {
	return DefaultService.DeleteAlarmType(ctx, typeID)
}

func UpdateAlarmTypeByTypeID(ctx context.Context, typeID string, businessID *string, name *string, desc *string, originTypeID *int64) error {
	return DefaultService.UpdateAlarmTypeByTypeID(ctx, typeID, businessID, name, desc, originTypeID)
}

func QueryAlarmTypeBySeveralConditions(ctx context.Context, filter AlarmTypeQueryFilter) ([]*AlarmTypeTable, error) {
	return DefaultService.QueryAlarmTypeBySeveralConditions(ctx, filter)
}

func QueryAlarmTypeCountBySeveralConditions(ctx context.Context, filter AlarmTypeQueryFilter) (int64, error) {
	return DefaultService.QueryAlarmTypeCountBySeveralConditions(ctx, filter)
}

// Public functions for TenantTable
func CreateTenant(ctx context.Context, tenant *TenantTable) (int64, error) {
	return DefaultService.CreateTenant(ctx, tenant)
}

func DeleteTenants(ctx context.Context, tenantIDs []string) error {
	return DefaultService.DeleteTenants(ctx, tenantIDs)
}

func UpdateTenantByTenantID(ctx context.Context, tenantID string, name *string, desc *string, exchangeID *int64) error {
	return DefaultService.UpdateTenantByTenantID(ctx, tenantID, name, desc, exchangeID)
}

func QueryTenantBySeveralConditions(ctx context.Context, filter TenantQueryFilter) ([]*TenantTable, error) {
	return DefaultService.QueryTenantBySeveralConditions(ctx, filter)
}

func QueryTenantCountBySeveralConditions(ctx context.Context, filter TenantQueryFilter) (int64, error) {
	return DefaultService.QueryTenantCountBySeveralConditions(ctx, filter)
}
