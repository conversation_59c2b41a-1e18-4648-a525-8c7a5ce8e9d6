/**
 * @note
 * alarm_type
 *
 * <AUTHOR>
 * @date 	2025-09-03
 */
package biz

import (
	"context"
	"gorm.io/gorm"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
)

// AlarmTypeTable implementation methods
func (m *BizModelImpl) CreateAlarmType(ctx context.Context, alarmType *AlarmTypeTable) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	alarmType.ID = uint(idgen.GetID())
	return int64(alarmType.ID), db.Create(alarmType).Error
}

func (m *BizModelImpl) DeleteAlarmType(ctx context.Context, typeID string) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	return db.Where("type_id = ?", typeID).Delete(&AlarmTypeTable{}).Error
}

func (m *BizModelImpl) UpdateAlarmTypeByTypeID(ctx context.Context, typeID string, businessID *string, name *string, desc *string, originTypeID *int64) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&AlarmTypeTable{})
	updateMap := make(map[string]interface{})

	if businessID != nil {
		updateMap["business_id"] = *businessID
	}
	if name != nil {
		updateMap["name"] = *name
	}
	if desc != nil {
		updateMap["desc"] = *desc
	}
	if originTypeID != nil {
		updateMap["origin_type_id"] = *originTypeID
	}

	if len(updateMap) == 0 {
		return nil // 没有需要更新的字段
	}

	return db.Where("type_id = ?", typeID).Updates(updateMap).Error
}

func (m *BizModelImpl) QueryAlarmTypeBySeveralConditions(ctx context.Context, filter AlarmTypeQueryFilter) ([]*AlarmTypeTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AlarmTypeTable{})

	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleAlarmTypeQueryConditions(ctx, filter, db)

	tbs := make([]*AlarmTypeTable, 0)
	if filter.Order != common.StringEmpty {
		db = db.Order(filter.Order)
	}
	db = db.Find(&tbs)
	return tbs, db.Error
}

func (m *BizModelImpl) QueryAlarmTypeCountBySeveralConditions(ctx context.Context, filter AlarmTypeQueryFilter) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&AlarmTypeTable{})

	db = assembleAlarmTypeQueryConditions(ctx, filter, db)
	var count int64
	db = db.Count(&count)
	return count, db.Error
}

func assembleAlarmTypeQueryConditions(ctx context.Context, filter AlarmTypeQueryFilter, db *gorm.DB) *gorm.DB {
	if filter.TypeID != common.StringEmpty {
		db = db.Where("type_id = ?", filter.TypeID)
	}
	if filter.BusinessID != common.StringEmpty {
		db = db.Where("business_id = ?", filter.BusinessID)
	}
	if filter.Name != common.StringEmpty {
		db = db.Where("name LIKE ?", "%"+filter.Name+"%")
	}
	if filter.OriginTypeID != 0 {
		db = db.Where("origin_type_id = ?", filter.OriginTypeID)
	}
	return db
}
