/**
 * @note
 * tenant
 *
 * <AUTHOR>
 * @date 	2025-09-02
 */
package biz

import (
	"context"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gitlab.docsl.com/security/socv2/soc/pkg/model/op_log"

	"gitlab.docsl.com/security/common"
	bizModel "gitlab.docsl.com/security/socv2/soc/internal/model/biz"
)

// wrapTenantTableToItem 将TenantTable转换为TenantItem
func wrapTenantTableToItem(tb *bizModel.TenantTable) *TenantItem {
	if tb == nil {
		return &TenantItem{}
	}
	return &TenantItem{
		TenantID:   tb.TenantID,
		Name:       tb.Name,
		Desc:       tb.Desc,
		ExchangeID: tb.ExchangeID,
		CreatedAt:  tb.CreatedAt,
		UpdatedAt:  tb.UpdatedAt,
	}
}

// QueryTenantList 查询平台列表
func QueryTenantList(ctx context.Context, page, perPage int, tenantIDs []string, name string) (items []*TenantItem, count int64, err error) {
	filter := bizModel.TenantQueryFilter{
		Page:      page,
		PerPage:   perPage,
		TenantIDs: tenantIDs,
		Name:      name,
	}
	tbs, err := bizModel.QueryTenantBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	count, err = bizModel.QueryTenantCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	items = make([]*TenantItem, 0, len(tbs))
	for _, tb := range tbs {
		items = append(items, wrapTenantTableToItem(tb))
	}
	return items, count, nil
}

func CreateTenant(ctx context.Context, tenantID string, name string, desc string, exchangeID int64) (id int64, err error) {
	tb := &bizModel.TenantTable{
		TenantID:   tenantID,
		Name:       name,
		Desc:       desc,
		ExchangeID: exchangeID,
	}
	id, err = bizModel.CreateTenant(ctx, tb)
	if err != nil {
		return id, err
	}
	tb.ID = uint(id)
	if err = op_log.CreateOperationLog(ctx, socCommon.OperationTenantCreate, &op_log.OperationDetail{
		Target: tb,
	}); err != nil {
		common.GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	return id, nil
}

func UpdateTenants(ctx context.Context, tenantID string, name, desc *string, exchangeID *int64) (err error) {
	// 查询原始数据用于日志记录
	originalTenant, err := bizModel.QueryTenantBySeveralConditions(ctx, bizModel.TenantQueryFilter{
		TenantIDs: []string{tenantID},
	})
	if err != nil {
		return err
	}
	if len(originalTenant) == 0 {
		return common.ErrRecordNotFound
	}
	err = bizModel.UpdateTenantByTenantID(ctx, tenantID, name, desc, exchangeID)
	if err != nil {
		return err
	}
	// 记录操作日志
	f := func(v *string) string {
		if v == nil {
			return common.StringEmpty
		}
		return *v
	}
	_ = op_log.CreateOperationLog(ctx, socCommon.OperationTenantUpdate, &op_log.OperationDetail{
		Target: tenantID,
		Before: wrapTenantTableToItem(originalTenant[0]),
		After: TenantItem{
			TenantID: tenantID,
			Name:     f(name),
			Desc:     f(desc),
			ExchangeID: func(v *int64) int64 {
				if v == nil {
					return 0
				}
				return *v
			}(exchangeID),
		},
	})
	return nil
}

// DeleteTenants 删除平台
func DeleteTenants(ctx context.Context, tenantIDs []string) (err error) {
	// 查询原始数据用于日志记录
	originalTenants, err := bizModel.QueryTenantBySeveralConditions(ctx, bizModel.TenantQueryFilter{
		TenantIDs: tenantIDs,
	})
	if err != nil {
		return err
	}
	existTenantIDs := make([]string, 0)
	for _, originalTenant := range originalTenants {
		existTenantIDs = append(existTenantIDs, originalTenant.TenantID)
	}
	if len(existTenantIDs) == 0 {
		return nil
	}

	err = bizModel.DeleteTenants(ctx, existTenantIDs)
	if err != nil {
		return err
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, socCommon.OperationTenantDelete, &op_log.OperationDetail{
		Target: existTenantIDs,
	})

	return nil
}
