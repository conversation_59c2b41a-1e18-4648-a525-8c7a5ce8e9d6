/**
 * @note
 * biz
 *
 * <AUTHOR>
 * @date 	2025-09-02
 */
package biz

import (
	"context"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gitlab.docsl.com/security/socv2/soc/pkg/model/op_log"

	"gitlab.docsl.com/security/common"
	bizModel "gitlab.docsl.com/security/socv2/soc/internal/model/biz"
)

// wrapBizTableToItem 将BizTable转换为BizItem
func wrapBizTableToItem(tb *bizModel.BizTable) *BizItem {
	if tb == nil {
		return &BizItem{}
	}
	return &BizItem{
		ID:               int64(tb.ID),
		BusinessID:       tb.BusinessID,
		Name:             tb.Name,
		Desc:             tb.Desc,
		OriginBusinessID: tb.OriginBusinessID,
		CreatedAt:        tb.CreatedAt,
		UpdatedAt:        tb.UpdatedAt,
	}
}

// QueryBizList 查询业务列表
func QueryBizList(ctx context.Context, page, perPage int, businessID, name string, originBusinessID int64) (items []*BizItem, count int64, err error) {
	filter := bizModel.BizQueryFilter{
		Page:             page,
		PerPage:          perPage,
		BusinessID:       businessID,
		Name:             name,
		OriginBusinessID: originBusinessID,
	}
	tbs, err := bizModel.QueryBizBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	count, err = bizModel.QueryBizCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	items = make([]*BizItem, 0, len(tbs))
	for _, tb := range tbs {
		items = append(items, wrapBizTableToItem(tb))
	}
	return items, count, nil
}

func CreateBiz(ctx context.Context, businessID string, name string, desc string, originBusinessID int64) (id int64, err error) {
	tb := &bizModel.BizTable{
		BusinessID:       businessID,
		Name:             name,
		Desc:             desc,
		OriginBusinessID: originBusinessID,
	}
	id, err = bizModel.CreateBiz(ctx, tb)
	if err != nil {
		return id, err
	}
	tb.ID = uint(id)
	if err = op_log.CreateOperationLog(ctx, socCommon.OperationBizCreate, &op_log.OperationDetail{
		Target: tb,
	}); err != nil {
		common.GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	return id, nil
}

func UpdateBiz(ctx context.Context, businessID string, name, desc *string, originBusinessID *int64) (err error) {
	// 查询原始数据用于日志记录
	originalBiz, err := bizModel.QueryBizBySeveralConditions(ctx, bizModel.BizQueryFilter{
		BusinessID: businessID,
	})
	if err != nil {
		return err
	}
	if len(originalBiz) == 0 {
		return common.ErrRecordNotFound
	}
	err = bizModel.UpdateBizByBizID(ctx, businessID, name, desc, originBusinessID)
	if err != nil {
		return err
	}
	// 记录操作日志
	f := func(v *string) string {
		if v == nil {
			return common.StringEmpty
		}
		return *v
	}
	_ = op_log.CreateOperationLog(ctx, socCommon.OperationBizUpdate, &op_log.OperationDetail{
		Target: businessID,
		Before: wrapBizTableToItem(originalBiz[0]),
		After: BizItem{
			BusinessID: businessID,
			Name:       f(name),
			Desc:       f(desc),
			OriginBusinessID: func(v *int64) int64 {
				if v == nil {
					return 0
				}
				return *v
			}(originBusinessID),
		},
	})
	return nil
}

// DeleteBiz 删除业务
func DeleteBiz(ctx context.Context, businessID string) (err error) {
	// 查询原始数据用于日志记录
	originalBiz, err := bizModel.QueryBizBySeveralConditions(ctx, bizModel.BizQueryFilter{
		BusinessID: businessID,
	})
	if err != nil {
		return err
	}
	if len(originalBiz) == 0 {
		return nil
	}

	err = bizModel.DeleteBiz(ctx, businessID)
	if err != nil {
		return err
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, socCommon.OperationBizDelete, &op_log.OperationDetail{
		Target: businessID,
	})

	return nil
}
