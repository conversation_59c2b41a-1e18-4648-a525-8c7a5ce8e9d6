/**
 * @note
 * biz logic structs
 *
 * <AUTHOR>
 * @date 	2025-09-02
 */
package biz

import (
	"time"
)

// TenantItem 平台信息
type TenantItem struct {
	ID         int64     `json:"id"`
	TenantID   string    `json:"tenantID"`
	Name       string    `json:"name"`
	Desc       string    `json:"desc"`
	ExchangeID int64     `json:"exchangeID"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}
