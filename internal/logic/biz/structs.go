/**
 * @note
 * biz logic structs
 *
 * <AUTHOR>
 * @date 	2025-09-02
 */
package biz

import (
	"time"
)

// TenantItem 平台信息
type TenantItem struct {
	ID         int64     `json:"id"`
	TenantID   string    `json:"tenantID"`
	Name       string    `json:"name"`
	Desc       string    `json:"desc"`
	ExchangeID int64     `json:"exchangeID"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

// BizItem 业务信息
type BizItem struct {
	ID               int64     `json:"id"`
	BusinessID       string    `json:"businessID"`
	Name             string    `json:"name"`
	Desc             string    `json:"desc"`
	OriginBusinessID int64     `json:"originBusinessID"`
	CreatedAt        time.Time `json:"createdAt"`
	UpdatedAt        time.Time `json:"updatedAt"`
}

// AlarmTypeItem 告警类型信息
type AlarmTypeItem struct {
	ID           int64     `json:"id"`
	TypeID       string    `json:"typeID"`
	BusinessID   string    `json:"businessID"`
	Name         string    `json:"name"`
	Desc         string    `json:"desc"`
	OriginTypeID int64     `json:"originTypeID"`
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}
