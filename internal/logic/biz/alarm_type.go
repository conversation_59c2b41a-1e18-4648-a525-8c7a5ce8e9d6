/**
 * @note
 * alarm_type
 *
 * <AUTHOR>
 * @date 	2025-09-02
 */
package biz

import (
	"context"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gitlab.docsl.com/security/socv2/soc/pkg/model/op_log"

	"gitlab.docsl.com/security/common"
	bizModel "gitlab.docsl.com/security/socv2/soc/internal/model/biz"
)

// wrapAlarmTypeTableToItem 将AlarmTypeTable转换为AlarmTypeItem
func wrapAlarmTypeTableToItem(tb *bizModel.AlarmTypeTable) *AlarmTypeItem {
	if tb == nil {
		return &AlarmTypeItem{}
	}
	return &AlarmTypeItem{
		ID:           int64(tb.ID),
		TypeID:       tb.TypeID,
		BusinessID:   tb.BusinessID,
		Name:         tb.Name,
		Desc:         tb.Desc,
		OriginTypeID: tb.OriginTypeID,
		CreatedAt:    tb.CreatedAt,
		UpdatedAt:    tb.UpdatedAt,
	}
}

// QueryAlarmTypeList 查询告警类型列表
func QueryAlarmTypeList(ctx context.Context, page, perPage int, typeID, businessID, name string, originTypeID int64) (items []*AlarmTypeItem, count int64, err error) {
	filter := bizModel.AlarmTypeQueryFilter{
		Page:         page,
		PerPage:      perPage,
		TypeID:       typeID,
		BusinessID:   businessID,
		Name:         name,
		OriginTypeID: originTypeID,
	}
	tbs, err := bizModel.QueryAlarmTypeBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	count, err := bizModel.QueryAlarmTypeCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	items = make([]*AlarmTypeItem, 0, len(tbs))
	for _, tb := range tbs {
		items = append(items, wrapAlarmTypeTableToItem(tb))
	}
	return items, count, nil
}

func CreateAlarmType(ctx context.Context, typeID string, businessID string, name string, desc string, originTypeID int64) (id int64, err error) {
	tb := &bizModel.AlarmTypeTable{
		TypeID:       typeID,
		BusinessID:   businessID,
		Name:         name,
		Desc:         desc,
		OriginTypeID: originTypeID,
	}
	id, err = bizModel.CreateAlarmType(ctx, tb)
	if err != nil {
		return id, err
	}
	tb.ID = uint(id)
	if err = op_log.CreateOperationLog(ctx, socCommon.OperationAlarmTypeCreate, &op_log.OperationDetail{
		Target: tb,
	}); err != nil {
		common.GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	return id, nil
}

func UpdateAlarmType(ctx context.Context, typeID string, businessID, name, desc *string, originTypeID *int64) (err error) {
	// 查询原始数据用于日志记录
	originalAlarmType, err := bizModel.QueryAlarmTypeBySeveralConditions(ctx, bizModel.AlarmTypeQueryFilter{
		TypeID: typeID,
	})
	if err != nil {
		return err
	}
	if len(originalAlarmType) == 0 {
		return common.ErrRecordNotFound
	}
	err = bizModel.UpdateAlarmTypeByTypeID(ctx, typeID, businessID, name, desc, originTypeID)
	if err != nil {
		return err
	}
	// 记录操作日志
	f := func(v *string) string {
		if v == nil {
			return common.StringEmpty
		}
		return *v
	}
	_ = op_log.CreateOperationLog(ctx, socCommon.OperationAlarmTypeUpdate, &op_log.OperationDetail{
		Target: typeID,
		Before: wrapAlarmTypeTableToItem(originalAlarmType[0]),
		After: AlarmTypeItem{
			TypeID:     typeID,
			BusinessID: f(businessID),
			Name:       f(name),
			Desc:       f(desc),
			OriginTypeID: func(v *int64) int64 {
				if v == nil {
					return 0
				}
				return *v
			}(originTypeID),
		},
	})
	return nil
}

// DeleteAlarmType 删除告警类型
func DeleteAlarmType(ctx context.Context, typeID string) (err error) {
	// 查询原始数据用于日志记录
	originalAlarmType, err := bizModel.QueryAlarmTypeBySeveralConditions(ctx, bizModel.AlarmTypeQueryFilter{
		TypeID: typeID,
	})
	if err != nil {
		return err
	}
	if len(originalAlarmType) == 0 {
		return nil
	}

	err = bizModel.DeleteAlarmType(ctx, typeID)
	if err != nil {
		return err
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, socCommon.OperationAlarmTypeDelete, &op_log.OperationDetail{
		Target: typeID,
	})

	return nil
}
