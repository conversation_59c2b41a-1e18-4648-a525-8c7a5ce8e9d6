/**
 * @note
 * filter_rule
 *
 * <AUTHOR>
 * @date 	2025-09-03
 */
package filter

import (
	"context"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gitlab.docsl.com/security/socv2/soc/pkg/model/op_log"

	"gitlab.docsl.com/security/common"
	filterModel "gitlab.docsl.com/security/socv2/soc/internal/model/filter"
)

// wrapFilterRuleTableToItem 将RuleTable转换为FilterRuleItem
func wrapFilterRuleTableToItem(tb *filterModel.RuleTable) *FilterRuleItem {
	if tb == nil {
		return &FilterRuleItem{}
	}
	return &FilterRuleItem{
		ID:           int64(tb.ID),
		RuleID:       tb.RuleID,
		BusinessID:   tb.BusinessID,
		TenantID:     tb.TenantID,
		Name:         tb.Name,
		Desc:         tb.Desc,
		Type:         tb.Type,
		FilterConfig: tb.FilterConfig,
		DedupConfig:  tb.DedupConfig,
		Status:       tb.Status,
		Version:      tb.Version,
		CreatedAt:    tb.CreatedAt,
		UpdatedAt:    tb.UpdatedAt,
	}
}

// QueryFilterRuleList 查询过滤规则列表
func QueryFilterRuleList(ctx context.Context, page, perPage int, ruleID, businessID, name string, tenantIDs []string, ruleType socCommon.FilterRuleType, status socCommon.FilterRuleStatus, version int64) (items []*FilterRuleItem, count int64, err error) {
	filter := filterModel.FilterRuleQueryFilter{
		Page:       page,
		PerPage:    perPage,
		RuleID:     ruleID,
		BusinessID: businessID,
		TenantIDs:  tenantIDs,
		Name:       name,
		Type:       ruleType,
		Status:     status,
		Version:    version,
	}
	tbs, err := filterModel.QueryFilterRuleBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	count, err = filterModel.QueryFilterRuleCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	items = make([]*FilterRuleItem, 0, len(tbs))
	for _, tb := range tbs {
		items = append(items, wrapFilterRuleTableToItem(tb))
	}
	return items, count, nil
}

// QueryLatestFilterRuleList 查询最新版本的过滤规则列表
func QueryLatestFilterRuleList(ctx context.Context, page, perPage int, ruleID, businessID, name string, tenantIDs []string, ruleType socCommon.FilterRuleType, status socCommon.FilterRuleStatus) (items []*FilterRuleItem, err error) {
	filter := filterModel.FilterRuleQueryFilter{
		Page:       page,
		PerPage:    perPage,
		RuleID:     ruleID,
		BusinessID: businessID,
		TenantIDs:  tenantIDs,
		Name:       name,
		Type:       ruleType,
		Status:     status,
	}
	tbs, err := filterModel.QueryLatestFilterRule(ctx, filter)
	if err != nil {
		return nil, err
	}
	items = make([]*FilterRuleItem, 0, len(tbs))
	for _, tb := range tbs {
		items = append(items, wrapFilterRuleTableToItem(tb))
	}
	return items, nil
}

func CreateFilterRule(ctx context.Context, ruleID string, businessID string, tenantID *filterModel.TenantIDArray, name string, desc string, ruleType socCommon.FilterRuleType, filterConfig *filterModel.FilterRuleConfig, dedupConfig *filterModel.DeduplicationRuleConfig, status socCommon.FilterRuleStatus, version int64) (id int64, err error) {
	tb := &filterModel.RuleTable{
		RuleID:       ruleID,
		BusinessID:   businessID,
		TenantID:     tenantID,
		Name:         name,
		Desc:         desc,
		Type:         ruleType,
		FilterConfig: filterConfig,
		DedupConfig:  dedupConfig,
		Status:       status,
		Version:      version,
	}
	id, err = filterModel.CreateFilterRule(ctx, tb)
	if err != nil {
		return id, err
	}
	tb.ID = uint(id)
	if err = op_log.CreateOperationLog(ctx, socCommon.OperationFilterRuleCreate, &op_log.OperationDetail{
		Target: tb,
	}); err != nil {
		common.GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	return id, nil
}

func UpdateFilterRule(ctx context.Context, ruleID string, businessID *string, tenantID *filterModel.TenantIDArray, name, desc *string, ruleType *socCommon.FilterRuleType, filterConfig *filterModel.FilterRuleConfig, dedupConfig *filterModel.DeduplicationRuleConfig, status *socCommon.FilterRuleStatus, version *int64) (err error) {
	// 查询原始数据用于日志记录
	originalFilterRule, err := filterModel.QueryFilterRuleBySeveralConditions(ctx, filterModel.FilterRuleQueryFilter{
		RuleID: ruleID,
	})
	if err != nil {
		return err
	}
	if len(originalFilterRule) == 0 {
		return common.ErrRecordNotFound
	}
	err = filterModel.UpdateFilterRuleByRuleID(ctx, ruleID, businessID, tenantID, name, desc, ruleType, filterConfig, dedupConfig, status, version)
	if err != nil {
		return err
	}
	// 记录操作日志
	f := func(v *string) string {
		if v == nil {
			return common.StringEmpty
		}
		return *v
	}
	_ = op_log.CreateOperationLog(ctx, socCommon.OperationFilterRuleUpdate, &op_log.OperationDetail{
		Target: ruleID,
		Before: wrapFilterRuleTableToItem(originalFilterRule[0]),
		After: FilterRuleItem{
			RuleID:     ruleID,
			BusinessID: f(businessID),
			TenantID:   tenantID,
			Name:       f(name),
			Desc:       f(desc),
			Type: func(v *socCommon.FilterRuleType) socCommon.FilterRuleType {
				if v == nil {
					return ""
				}
				return *v
			}(ruleType),
			FilterConfig: filterConfig,
			DedupConfig:  dedupConfig,
			Status: func(v *socCommon.FilterRuleStatus) socCommon.FilterRuleStatus {
				if v == nil {
					return ""
				}
				return *v
			}(status),
			Version: func(v *int64) int64 {
				if v == nil {
					return 0
				}
				return *v
			}(version),
		},
	})
	return nil
}

// DeleteFilterRule 删除过滤规则
func DeleteFilterRule(ctx context.Context, ruleID string) (err error) {
	// 查询原始数据用于日志记录
	originalFilterRule, err := filterModel.QueryFilterRuleBySeveralConditions(ctx, filterModel.FilterRuleQueryFilter{
		RuleID: ruleID,
	})
	if err != nil {
		return err
	}
	if len(originalFilterRule) == 0 {
		return nil
	}

	err = filterModel.DeleteFilterRule(ctx, ruleID)
	if err != nil {
		return err
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, socCommon.OperationFilterRuleDelete, &op_log.OperationDetail{
		Target: ruleID,
	})

	return nil
}
