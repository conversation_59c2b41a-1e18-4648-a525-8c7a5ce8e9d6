package filter

import (
	"github.com/kataras/iris/v12"

	. "gitlab.docsl.com/security/common"
	filterLogic "gitlab.docsl.com/security/socv2/soc/internal/logic/filter"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
)

func QueryFilterRuleList(ctx iris.Context) {
	req := &ListFilterRuleRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	var err error
	ret := &ListFilterRuleResponse{}
	ret.Items, ret.Total, err = filterLogic.QueryFilterRuleList(ctx, req.<PERSON>, req.PerPage,
		req.RuleID, req.BusinessID, req.Name, req.TenantIDs, req.Type, req.Status, req.Version)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrQueryFilterRuleList, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(ret))
}

func QueryLatestFilterRuleList(ctx iris.Context) {
	req := &ListLatestFilterRuleRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	var err error
	ret := &ListLatestFilterRuleResponse{}
	ret.Items, err = filterLogic.QueryLatestFilterRuleList(ctx, req.Page, req.PerPage,
		req.RuleID, req.BusinessID, req.Name, req.TenantIDs, req.Type, req.Status)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrQueryFilterRuleList, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(ret))
}

func CreateFilterRule(ctx iris.Context) {
	req := &CreateFilterRuleRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	_, err := filterLogic.CreateFilterRule(ctx, req.RuleID, req.BusinessID, req.TenantID, req.Name, req.Desc, req.Type, req.FilterConfig, req.DedupConfig, req.Status, req.Version)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrCreateFilterRule, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func UpdateFilterRule(ctx iris.Context) {
	req := &UpdateFilterRuleRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := filterLogic.UpdateFilterRule(ctx, req.RuleID, req.BusinessID, req.TenantID, req.Name, req.Desc, req.Type, req.FilterConfig, req.DedupConfig, req.Status, req.Version)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrUpdateFilterRule, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func DeleteFilterRule(ctx iris.Context) {
	req := &DeleteFilterRuleRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := filterLogic.DeleteFilterRule(ctx, req.RuleID)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrDeleteFilterRule, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}
