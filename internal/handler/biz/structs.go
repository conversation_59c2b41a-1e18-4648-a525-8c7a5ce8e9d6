/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-09-02
 */
package biz

import (
	bizLogic "gitlab.docsl.com/security/socv2/soc/internal/logic/biz"
)

type ListTenantRequest struct {
	Page      int      `json:"page" validate:"gte=0"`
	PerPage   int      `json:"perPage" validate:"gt=0"`
	TenantIDs []string `json:"tenantIDs"`
	Name      string   `json:"name"`
}

type ListTenantResponse struct {
	Total int64                  `json:"total"`
	Items []*bizLogic.TenantItem `json:"items"`
}

type CreateTenantRequest struct {
	TenantID   string `json:"tenantID" validate:"required"`
	Name       string `json:"name" validate:"required"`
	Desc       string `json:"desc"`
	ExchangeID int64  `json:"exchangeID"`
}

type CreateTenantResponse struct {
	ID uint `json:"id"`
}

type UpdateTenantRequest struct {
	TenantID   string  `json:"tenantID" validate:"required"`
	Name       *string `json:"name"`
	Desc       *string `json:"desc"`
	ExchangeID *int64  `json:"exchangeID"`
}

type DeleteTenantRequest struct {
	TenantIDs []string `json:"tenantIDs" validate:"required"`
}
