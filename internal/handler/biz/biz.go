package biz

import (
	"github.com/kataras/iris/v12"

	. "gitlab.docsl.com/security/common"
	bizLogic "gitlab.docsl.com/security/socv2/soc/internal/logic/biz"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
)

func QueryBizList(ctx iris.Context) {
	req := &ListBizRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	var err error
	ret := &ListBizResponse{}
	ret.Items, ret.Total, err = bizLogic.QueryBizList(ctx, req.Page, req.PerPage,
		req.BusinessID, req.Name, req.OriginBusinessID)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrQueryBizList, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(ret))
}

func CreateBiz(ctx iris.Context) {
	req := &CreateBizRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	_, err := bizLogic.CreateBiz(ctx, req.BusinessID, req.Name, req.Desc, req.OriginBusinessID)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrCreateBiz, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func UpdateBiz(ctx iris.Context) {
	req := &UpdateBizRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := bizLogic.UpdateBiz(ctx, req.BusinessID, req.Name, req.Desc, req.OriginBusinessID)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrUpdateBiz, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func DeleteBiz(ctx iris.Context) {
	req := &DeleteBizRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := bizLogic.DeleteBiz(ctx, req.BusinessID)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrDeleteBiz, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}
