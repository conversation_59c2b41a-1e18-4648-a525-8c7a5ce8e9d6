package biz

import (
	"github.com/kataras/iris/v12"

	. "gitlab.docsl.com/security/common"
	bizLogic "gitlab.docsl.com/security/socv2/soc/internal/logic/biz"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
)

func QueryTenantList(ctx iris.Context) {
	req := &ListTenantRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	var err error
	ret := &ListTenantResponse{}
	ret.Items, ret.Total, err = bizLogic.QueryTenantList(ctx, req.<PERSON>, req.Per<PERSON><PERSON>,
		req.TenantIDs, req.Name)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrQueryTenantList, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(ret))
}

func CreateTenant(ctx iris.Context) {
	req := &CreateTenantRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	_, err := bizLogic.CreateTenant(ctx, req.TenantID, req.Name, req.Desc, req.ExchangeID)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrCreateTenant, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func UpdateTenant(ctx iris.Context) {
	req := &UpdateTenantRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := bizLogic.UpdateTenants(ctx, req.TenantID, req.Name, req.Desc, req.ExchangeID)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrUpdateTenant, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func DeleteTenants(ctx iris.Context) {
	req := &DeleteTenantRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := bizLogic.DeleteTenants(ctx, req.TenantIDs)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrDeleteTenant, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}
