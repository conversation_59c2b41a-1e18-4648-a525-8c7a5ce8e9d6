package biz

import (
	"github.com/kataras/iris/v12"

	. "gitlab.docsl.com/security/common"
	bizLogic "gitlab.docsl.com/security/socv2/soc/internal/logic/biz"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
)

func QueryAlarmTypeList(ctx iris.Context) {
	req := &ListAlarmTypeRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	var err error
	ret := &ListAlarmTypeResponse{}
	ret.Items, ret.Total, err = bizLogic.QueryAlarmTypeList(ctx, req.<PERSON>, req.PerPage,
		req.TypeID, req.BusinessID, req.Name, req.OriginTypeID)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrQueryAlarmTypeList, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(ret))
}

func CreateAlarmType(ctx iris.Context) {
	req := &CreateAlarmTypeRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	_, err := bizLogic.CreateAlarmType(ctx, req.TypeID, req.BusinessID, req.Name, req.Desc, req.OriginTypeID)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrCreateAlarmType, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func UpdateAlarmType(ctx iris.Context) {
	req := &UpdateAlarmTypeRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := bizLogic.UpdateAlarmType(ctx, req.TypeID, req.BusinessID, req.Name, req.Desc, req.OriginTypeID)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrUpdateAlarmType, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func DeleteAlarmType(ctx iris.Context) {
	req := &DeleteAlarmTypeRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := bizLogic.DeleteAlarmType(ctx, req.TypeID)
	if err != nil {
		SetRet(ctx, NewError(socCommon.ErrDeleteAlarmType, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}
