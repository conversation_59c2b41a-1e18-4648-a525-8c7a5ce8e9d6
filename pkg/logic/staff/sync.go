package staff

import (
	"context"
	"fmt"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	"gitlab.docsl.com/security/common"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gitlab.docsl.com/security/socv2/soc/pkg/helper/lark"
	"gitlab.docsl.com/security/socv2/soc/pkg/model/staff"
	"time"
)

// SyncLarkData 同步 Lark 部门和员工数据到本地数据库
func SyncLarkData(ctx context.Context) error {
	// 获取 Lark 客户端
	cli := lark.GetLarkClient()
	if cli == nil {
		return fmt.Errorf("failed to get lark client")
	}

	// 第一步：获取所有部门
	common.GetLogger(ctx).Infoln("begin to fetch departments from lark")
	departments, err := lark.GetDepartmentChildren(ctx, cli, "0", true, 50)
	if err != nil {
		return fmt.Errorf("failed to get departments: %w", err)
	}
	common.GetLogger(ctx).Infof("fetched %d departments", len(departments))

	// 第二步：获取所有用户
	common.GetLogger(ctx).Infoln("begin to fetch users in department from lark")
	allUsers := make([]*larkcontact.User, 0)
	userDeptRelations := make([]*staff.UserDepartmentTableRelation, 0)

	for _, dept := range departments {
		if dept.DepartmentId == nil {
			continue
		}
		time.Sleep(time.Millisecond * 200) // 休息200毫秒，别触发了lark限流
		users, err := lark.GetDepartmentUsers(ctx, cli, *dept.DepartmentId, 50)
		if err != nil {
			common.GetLogger(ctx).Errorf("fetching users in department %s failed: %v\n", *dept.DepartmentId, err)
			return err
		}

		common.GetLogger(ctx).Infof("department %s fetched %d users", *dept.DepartmentId, len(users))

		// 收集用户数据
		allUsers = append(allUsers, users...)

		// 创建用户部门关系
		for _, user := range users {
			if user.UserId == nil {
				continue
			}
			relation := &staff.UserDepartmentTableRelation{
				LarkUserID:       *user.UserId,
				LarkDepartmentID: *dept.DepartmentId,
			}
			for _, order := range user.Orders {
				if order.DepartmentId != nil && *order.DepartmentId == *dept.DepartmentId {
					if order.UserOrder != nil {
						relation.UserOrder = *order.UserOrder
					}
					if order.DepartmentOrder != nil {
						relation.DepartmentOrder = *order.DepartmentOrder
					}
					if order.IsPrimaryDept != nil {
						relation.IsPrimaryDept = *order.IsPrimaryDept
					}
				}
			}
			userDeptRelations = append(userDeptRelations, relation)
		}
	}

	common.GetLogger(ctx).Infof("fetched %d users total", len(allUsers))

	// 第三步：转换并保存部门数据
	common.GetLogger(ctx).Infoln("begin to save departments")
	departmentTables := make([]*staff.DepartmentTable, 0)
	for _, dept := range departments {
		if dept.DepartmentId == nil {
			continue
		}

		deptTable := &staff.DepartmentTable{
			Name:             *dept.Name,
			LarkDepartmentID: *dept.DepartmentId,
			Status: func(status *larkcontact.DepartmentStatus) socCommon.StaffStatusEnum {
				if status != nil && status.IsDeleted != nil && *status.IsDeleted {
					return socCommon.StatusDeleted
				}
				return socCommon.StatusNormal
			}(dept.Status),
		}

		// 设置国际化名称
		if dept.I18nName != nil {
			deptTable.I18nName, err = common.JsonStringEncode(dept.I18nName)
			if err != nil {
				return err
			}
		}

		// 设置父部门ID
		if dept.ParentDepartmentId != nil {
			deptTable.LarkParentDepartmentID = *dept.ParentDepartmentId
		}

		// 设置部门负责人
		if dept.LeaderUserId != nil {
			deptTable.LarkLeaderUserID = *dept.LeaderUserId
		}

		// 设置排序
		if dept.Order != nil {
			deptTable.Order = *dept.Order
		}

		departmentTables = append(departmentTables, deptTable)
	}

	// 批量保存部门数据
	if len(departmentTables) > 0 {
		err = staff.CreateOrUpdateDepartments(ctx, departmentTables)
		if err != nil {
			return fmt.Errorf("failed to save departments: %w", err)
		}
		common.GetLogger(ctx).Infof("save %d departments successfully", len(departmentTables))
	}

	// 第四步：转换并保存用户数据
	common.GetLogger(ctx).Infoln("begin to save users")
	userTables := make([]*staff.UserTable, 0)
	userMap := make(map[string]*staff.UserTable) // 用于去重

	for _, user := range allUsers {
		if user.UserId == nil {
			continue
		}

		// 检查是否已经处理过这个用户（去重）
		if _, exists := userMap[*user.UserId]; exists {
			continue
		}

		userTable := &staff.UserTable{
			LarkUserID: *user.UserId,
			Status:     lark.GetSocUserStatusByLarkUserStatus(user.Status),
		}

		// 设置用户名称
		if user.Name != nil {
			userTable.LarkName = *user.Name
		}

		// 设置英文名称
		if user.EnName != nil {
			userTable.LarkEnName = *user.EnName
		}

		// 设置邮箱
		if user.Email != nil {
			userTable.Email = *user.Email
		}

		// 设置手机号
		if user.Mobile != nil {
			userTable.Mobile = *user.Mobile
		}

		// 设置性别
		if user.Gender != nil {
			userTable.Gender = *user.Gender
		}

		// 设置员工工号
		if user.EmployeeNo != nil {
			userTable.EmployeeNo = *user.EmployeeNo
		}

		// 设置员工类型
		if user.EmployeeType != nil {
			userTable.EmployeeType = *user.EmployeeType
		}

		// 设置城市
		if user.City != nil {
			userTable.City = *user.City
		}

		// 设置国家
		if user.Country != nil {
			userTable.Country = *user.Country
		}

		// 设置入职时间
		if user.JoinTime != nil {
			userTable.JoinTime = int64(*user.JoinTime)
		}

		// 设置职位
		if user.JobTitle != nil {
			userTable.JobTitle = *user.JobTitle
		}

		// 设置直属领导
		if user.LeaderUserId != nil {
			userTable.LarkLeaderUserID = *user.LeaderUserId
		}

		userTables = append(userTables, userTable)
		userMap[*user.UserId] = userTable
	}

	// 批量保存用户数据
	if len(userTables) > 0 {
		err = staff.DefaultService.CreateOrUpdateUsers(ctx, userTables)
		if err != nil {
			return fmt.Errorf("failed to save users: %w", err)
		}
		common.GetLogger(ctx).Infof("save %d users successfully", len(userTables))
	}

	common.GetLogger(ctx).Infoln("begin to save user department relations")
	err = staff.TruncateUserDepartmentRelations(ctx)
	if err != nil {
		return fmt.Errorf("failed to truncate old user department relations: %w", err)
	}
	if len(userDeptRelations) > 0 {
		err = staff.CreateUserDepartmentRelation(ctx, userDeptRelations)
		if err != nil {
			return fmt.Errorf("failed to save user department relations: %w", err)
		}
		common.GetLogger(ctx).Infof("safe %d user department relations successfully", len(userDeptRelations))
	}

	common.GetLogger(ctx).Infoln("Lark contact sync successfully！")
	return nil
}
